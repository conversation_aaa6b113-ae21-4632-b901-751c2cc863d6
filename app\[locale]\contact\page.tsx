import { Metadata } from 'next';
import { Locale } from '../../../lib/i18n';
import { generateContactMetadata } from '../../../lib/metadata';
import Navbar from '../../../components/Navbar';
import ContactContent from '../../../components/ContactContent';

// إنشاء metadata لصفحة التواصل
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;
  return generateContactMetadata(locale);
}

export default async function ContactPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  return (
    <>
      <Navbar locale={locale} />
      <ContactContent locale={locale} />
    </>
  );
}
