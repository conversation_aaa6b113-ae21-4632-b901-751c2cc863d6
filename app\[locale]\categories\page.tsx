import { Locale } from '../../../lib/i18n';
import CategoriesPageComponent from '../../../components/CategoriesPage';
import PageSEO from '../../../components/SEO/PageSEO';
import { WebPageJsonLdComponent } from '../../../components/SEO/JsonLd';

interface PageProps {
  params: Promise<{
    locale: Locale;
  }>;
}

export default async function Categories({ params }: PageProps) {
  const { locale } = await params;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  return (
    <>
      {/* SEO Components */}
      <PageSEO
        locale={locale}
        page="categories"
        customData={{
          canonical: `${baseUrl}/${locale}/categories`,
        }}
      />
      <WebPageJsonLdComponent
        locale={locale}
        title={locale === 'ar' ? 'فئات المنتجات - تصفح حسب الفئة' : 'Product Categories - Browse by Category'}
        description={locale === 'ar'
          ? 'استكشف فئات منتجاتنا المتنوعة. تصفح بسهولة حسب الفئة للعثور على ما تبحث عنه بسرعة وسهولة.'
          : 'Explore our diverse product categories. Browse easily by category to find what you are looking for quickly and easily.'}
        url={`${baseUrl}/${locale}/categories`}
        breadcrumbs={[
          {
            name: locale === 'ar' ? 'الرئيسية' : 'Home',
            url: `${baseUrl}/${locale}`,
          },
          {
            name: locale === 'ar' ? 'الفئات' : 'Categories',
            url: `${baseUrl}/${locale}/categories`,
          },
        ]}
      />

      <CategoriesPageComponent locale={locale} />
    </>
  );
}
