import { NextRequest, NextResponse } from 'next/server';
import { getProducts } from '../../lib/mysql-database';
import { getCategories } from '../../lib/mysql-database';
import { getSubcategories } from '../../lib/mysql-database';

// دالة لإنشاء XML للـ sitemap
function generateSitemapXML(urls: Array<{
  loc: string;
  lastmod: string;
  changefreq: string;
  priority: string;
}>) {
  const urlsXML = urls.map(url => `
  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
  </url>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlsXML}
</urlset>`;
}

export async function GET(request: NextRequest) {
  try {
    const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
    const currentTime = new Date().toISOString();
    const urls = [];

    // الصفحات الثابتة
    const staticPages = [
      { loc: `${siteUrl}/ar`, changefreq: 'daily', priority: '1.0' },
      { loc: `${siteUrl}/en`, changefreq: 'daily', priority: '1.0' },
      { loc: `${siteUrl}/ar/products`, changefreq: 'daily', priority: '0.9' },
      { loc: `${siteUrl}/en/products`, changefreq: 'daily', priority: '0.9' },
      { loc: `${siteUrl}/ar/categories`, changefreq: 'weekly', priority: '0.8' },
      { loc: `${siteUrl}/en/categories`, changefreq: 'weekly', priority: '0.8' },
      { loc: `${siteUrl}/ar/about`, changefreq: 'monthly', priority: '0.6' },
      { loc: `${siteUrl}/en/about`, changefreq: 'monthly', priority: '0.6' },
      { loc: `${siteUrl}/ar/contact`, changefreq: 'monthly', priority: '0.6' },
      { loc: `${siteUrl}/en/contact`, changefreq: 'monthly', priority: '0.6' },
    ];

    // إضافة الصفحات الثابتة
    staticPages.forEach(page => {
      urls.push({
        ...page,
        lastmod: currentTime,
      });
    });

    // جلب المنتجات
    try {
      const products = await getProducts();
      if (products && products.length > 0) {
        products.forEach((product: any) => {
          const lastmod = product.updated_at ? new Date(product.updated_at).toISOString() : currentTime;
          
          urls.push({
            loc: `${siteUrl}/ar/product/${product.id}`,
            changefreq: 'weekly',
            priority: '0.8',
            lastmod: lastmod,
          });
          
          urls.push({
            loc: `${siteUrl}/en/product/${product.id}`,
            changefreq: 'weekly',
            priority: '0.8',
            lastmod: lastmod,
          });
        });
      }
    } catch (error) {
      console.error('Error fetching products for sitemap:', error);
    }

    // جلب الفئات
    try {
      const categories = await getCategories();
      if (categories && categories.length > 0) {
        categories.forEach((category: any) => {
          const lastmod = category.updated_at ? new Date(category.updated_at).toISOString() : currentTime;
          
          urls.push({
            loc: `${siteUrl}/ar/category/${category.id}`,
            changefreq: 'weekly',
            priority: '0.7',
            lastmod: lastmod,
          });
          
          urls.push({
            loc: `${siteUrl}/en/category/${category.id}`,
            changefreq: 'weekly',
            priority: '0.7',
            lastmod: lastmod,
          });
        });
      }
    } catch (error) {
      console.error('Error fetching categories for sitemap:', error);
    }

    // جلب الفئات الفرعية
    try {
      const subcategories = await getSubcategories();
      if (subcategories && subcategories.length > 0) {
        subcategories.forEach((subcategory: any) => {
          const lastmod = subcategory.updated_at ? new Date(subcategory.updated_at).toISOString() : currentTime;
          
          urls.push({
            loc: `${siteUrl}/ar/subcategory/${subcategory.id}`,
            changefreq: 'weekly',
            priority: '0.6',
            lastmod: lastmod,
          });
          
          urls.push({
            loc: `${siteUrl}/en/subcategory/${subcategory.id}`,
            changefreq: 'weekly',
            priority: '0.6',
            lastmod: lastmod,
          });
        });
      }
    } catch (error) {
      console.error('Error fetching subcategories for sitemap:', error);
    }

    // إنشاء sitemap XML
    const sitemapXML = generateSitemapXML(urls);

    // إرجاع XML مع headers صحيحة
    return new NextResponse(sitemapXML, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // تخزين مؤقت لساعة واحدة
      },
    });

  } catch (error) {
    console.error('Error generating sitemap:', error);
    return NextResponse.json(
      { error: 'Failed to generate sitemap' },
      { status: 500 }
    );
  }
}
