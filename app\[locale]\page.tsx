import dynamic from 'next/dynamic';
import { Locale } from '../../lib/i18n';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import HeroSection from '../../components/HeroSection';
import FeaturedProducts from '../../components/FeaturedProducts';
import CategoriesSection from '../../components/CategoriesSection';
import ServicesSection from '../../components/ServicesSection';
import PartnersSection from '../../components/PartnersSection';
import PageSEO from '../../components/SEO/PageSEO';
import { OrganizationJsonLdComponent, WebPageJsonLdComponent } from '../../components/SEO/JsonLd';

// تحميل ديناميكي للمكونات غير الضرورية للتحميل الأولي
const WhatsAppButton = dynamic(() => import('../../components/WhatsAppButton'), {
  loading: () => null
});

// لا نحتاج generateMetadata مع next-seo

export default async function HomePage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  return (
    <>
      {/* SEO Components */}
      <PageSEO
        locale={locale}
        page="home"
        customData={{
          canonical: `${baseUrl}/${locale}`,
        }}
      />
      <OrganizationJsonLdComponent locale={locale} />
      <WebPageJsonLdComponent
        locale={locale}
        title={locale === 'ar' ? 'الصفحة الرئيسية - متجرك الموثوق للتسوق الإلكتروني' : 'Home - Your Trusted Online Shopping Store'}
        description={locale === 'ar'
          ? 'اكتشف مجموعة واسعة من المنتجات عالية الجودة في دروب هاجر. تسوق بثقة مع ضمان الجودة وخدمة العملاء المميزة.'
          : 'Discover a wide range of high-quality products at DROOB HAJER. Shop with confidence with quality guarantee and excellent customer service.'}
        url={`${baseUrl}/${locale}`}
        breadcrumbs={[
          {
            name: locale === 'ar' ? 'الرئيسية' : 'Home',
            url: `${baseUrl}/${locale}`,
          },
        ]}
      />

      <Navbar locale={locale} />
      <main>
        <HeroSection locale={locale} />
        <ServicesSection locale={locale} />
        <CategoriesSection locale={locale} />
        <FeaturedProducts locale={locale} />
        <PartnersSection locale={locale} />
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
