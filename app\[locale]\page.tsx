import dynamic from 'next/dynamic';
import { Metadata } from 'next';
import { Locale } from '../../lib/i18n';
import { generateHomeMetadata } from '../../lib/metadata';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import HeroSection from '../../components/HeroSection';
import FeaturedProducts from '../../components/FeaturedProducts';
import CategoriesSection from '../../components/CategoriesSection';
import ServicesSection from '../../components/ServicesSection';
import PartnersSection from '../../components/PartnersSection';

// تحميل ديناميكي للمكونات غير الضرورية للتحميل الأولي
const WhatsAppButton = dynamic(() => import('../../components/WhatsAppButton'), {
  loading: () => null
});

// إنشاء metadata للصفحة الرئيسية
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;
  return generateHomeMetadata(locale);
}

export default async function HomePage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  return (
    <>
      <Navbar locale={locale} />
      <main>
        <HeroSection locale={locale} />
        <ServicesSection locale={locale} />
        <CategoriesSection locale={locale} />
        <FeaturedProducts locale={locale} />
        <PartnersSection locale={locale} />
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
