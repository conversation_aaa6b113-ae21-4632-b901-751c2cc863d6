import { Metadata } from 'next';
import { Locale } from '../../../lib/i18n';
import { generateAboutMetadata } from '../../../lib/metadata';
import Navbar from '../../../components/Navbar';
import AboutContent from '../../../components/AboutContent';

// إنشاء metadata لصفحة من نحن
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;
  return generateAboutMetadata(locale);
}

export default async function AboutPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  return (
    <>
      <Navbar locale={locale} />
      <AboutContent locale={locale} />
    </>
  );
}


