import { Suspense } from 'react';
import { Locale } from '../../../lib/i18n';
import Navbar from '../../../components/Navbar';
import ProductsContent from '../../../components/ProductsContent';
import PageSEO from '../../../components/SEO/PageSEO';
import { WebPageJsonLdComponent } from '../../../components/SEO/JsonLd';

// لا نحتاج generateMetadata مع next-seo

// Loading component for Suspense fallback
function ProductsLoading() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-primary py-12">
        <div className="container mx-auto px-4">
          <div className="animate-pulse">
            <div className="h-8 bg-white/20 rounded w-48 mx-auto mb-4"></div>
            <div className="h-4 bg-white/10 rounded w-96 mx-auto"></div>
          </div>
        </div>
      </div>
      <div className="container mx-auto px-4 py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    </div>
  );
}

// Main page component
export default async function ProductsPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  return (
    <>
      {/* SEO Components */}
      <PageSEO
        locale={locale}
        page="products"
        customData={{
          canonical: `${baseUrl}/${locale}/products`,
        }}
      />
      <WebPageJsonLdComponent
        locale={locale}
        title={locale === 'ar' ? 'جميع المنتجات - تشكيلة واسعة ومتنوعة' : 'All Products - Wide and Diverse Collection'}
        description={locale === 'ar'
          ? 'تصفح جميع منتجاتنا المتنوعة وعالية الجودة. اكتشف أحدث العروض والمنتجات الجديدة مع أفضل الأسعار وضمان الجودة.'
          : 'Browse all our diverse and high-quality products. Discover the latest offers and new products with the best prices and quality guarantee.'}
        url={`${baseUrl}/${locale}/products`}
        breadcrumbs={[
          {
            name: locale === 'ar' ? 'الرئيسية' : 'Home',
            url: `${baseUrl}/${locale}`,
          },
          {
            name: locale === 'ar' ? 'المنتجات' : 'Products',
            url: `${baseUrl}/${locale}/products`,
          },
        ]}
      />

      <Navbar locale={locale} />
      <Suspense fallback={<ProductsLoading />}>
        <ProductsContent locale={locale} />
      </Suspense>
    </>
  );
}
