import { Metadata } from 'next';
import { Suspense } from 'react';
import { Locale } from '../../../lib/i18n';
import { generateProductsMetadata } from '../../../lib/metadata';
import Navbar from '../../../components/Navbar';
import ProductsContent from '../../../components/ProductsContent';

// إنشاء metadata لصفحة المنتجات
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;
  return generateProductsMetadata(locale);
}

// Loading component for Suspense fallback
function ProductsLoading() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-primary py-12">
        <div className="container mx-auto px-4">
          <div className="animate-pulse">
            <div className="h-8 bg-white/20 rounded w-48 mx-auto mb-4"></div>
            <div className="h-4 bg-white/10 rounded w-96 mx-auto"></div>
          </div>
        </div>
      </div>
      <div className="container mx-auto px-4 py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    </div>
  );
}

// Main page component
export default async function ProductsPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  return (
    <>
      <Navbar locale={locale} />
      <Suspense fallback={<ProductsLoading />}>
        <ProductsContent locale={locale} />
      </Suspense>
    </>
  );
}
