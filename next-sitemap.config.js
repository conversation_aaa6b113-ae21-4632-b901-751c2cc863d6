/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com',
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  exclude: [
    '/admin/*',
    '/api/*',
    '/cart',
    '/checkout',
    '/profile',
    '/login',
    '/register',
    '/404',
    '/500',
    '/server-sitemap-index.xml'
  ],
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/admin/',
          '/api/',
          '/cart',
          '/checkout',
          '/profile',
          '/login',
          '/register',
          '/_next/',
          '/static/',
          '*.json',
          '*.xml'
        ],
      },
    ],
    additionalSitemaps: [
      'https://droobhajer.com/sitemap.xml',
    ],
  },
  transform: async (config, path) => {
    // تخصيص الأولوية وتكرار التحديث حسب نوع الصفحة
    let priority = 0.7;
    let changefreq = 'weekly';

    // الصفحة الرئيسية
    if (path === '/ar' || path === '/en' || path === '/') {
      priority = 1.0;
      changefreq = 'daily';
    }
    // صفحات المنتجات
    else if (path.includes('/products')) {
      priority = 0.9;
      changefreq = 'daily';
    }
    // صفحات الفئات
    else if (path.includes('/categories')) {
      priority = 0.8;
      changefreq = 'weekly';
    }
    // صفحات ثابتة مهمة
    else if (path.includes('/about') || path.includes('/contact')) {
      priority = 0.6;
      changefreq = 'monthly';
    }

    return {
      loc: path,
      changefreq,
      priority,
      lastmod: new Date().toISOString(),
    };
  },
  additionalPaths: async () => {
    const currentTime = new Date().toISOString();
    return [
      {
        loc: '/ar',
        changefreq: 'daily',
        priority: 1.0,
        lastmod: currentTime,
      },
      {
        loc: '/en',
        changefreq: 'daily',
        priority: 1.0,
        lastmod: currentTime,
      },
      {
        loc: '/ar/products',
        changefreq: 'daily',
        priority: 0.9,
        lastmod: currentTime,
      },
      {
        loc: '/en/products',
        changefreq: 'daily',
        priority: 0.9,
        lastmod: currentTime,
      },
      {
        loc: '/ar/categories',
        changefreq: 'weekly',
        priority: 0.8,
        lastmod: currentTime,
      },
      {
        loc: '/en/categories',
        changefreq: 'weekly',
        priority: 0.8,
        lastmod: currentTime,
      },
      {
        loc: '/ar/about',
        changefreq: 'monthly',
        priority: 0.6,
        lastmod: currentTime,
      },
      {
        loc: '/en/about',
        changefreq: 'monthly',
        priority: 0.6,
        lastmod: currentTime,
      },
      {
        loc: '/ar/contact',
        changefreq: 'monthly',
        priority: 0.6,
        lastmod: currentTime,
      },
      {
        loc: '/en/contact',
        changefreq: 'monthly',
        priority: 0.6,
        lastmod: currentTime,
      },
    ];
  },
  autoLastmod: true,
  sitemapSize: 5000,
  outDir: './public',
};
