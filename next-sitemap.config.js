/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com',
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  exclude: [
    '/admin/*',
    '/api/*',
    '/cart',
    '/checkout',
    '/profile',
    '/login',
    '/register',
    '/404',
    '/500',
    '/server-sitemap-index.xml'
  ],
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/admin/',
          '/api/',
          '/cart',
          '/checkout',
          '/profile',
          '/login',
          '/register',
          '/_next/',
          '/static/',
          '*.json',
          '*.xml'
        ],
      },
    ],
    additionalSitemaps: [
      'https://droobhajer.com/sitemap.xml',
    ],
  },
  transform: async (config, path) => {
    // تخصيص الأولوية وتكرار التحديث حسب نوع الصفحة
    let priority = 0.7;
    let changefreq = 'weekly';

    // الصفحة الرئيسية
    if (path === '/ar' || path === '/en' || path === '/') {
      priority = 1.0;
      changefreq = 'daily';
    }
    // صفحات المنتجات
    else if (path.includes('/products')) {
      priority = 0.9;
      changefreq = 'daily';
    }
    // صفحات الفئات
    else if (path.includes('/categories')) {
      priority = 0.8;
      changefreq = 'weekly';
    }
    // صفحات ثابتة مهمة
    else if (path.includes('/about') || path.includes('/contact')) {
      priority = 0.6;
      changefreq = 'monthly';
    }

    return {
      loc: path,
      changefreq,
      priority,
      lastmod: new Date().toISOString(),
    };
  },
  additionalPaths: async () => {
    const currentTime = new Date().toISOString();
    const paths = [];

    // الصفحات الثابتة
    const staticPages = [
      { loc: '/ar', changefreq: 'daily', priority: 1.0 },
      { loc: '/en', changefreq: 'daily', priority: 1.0 },
      { loc: '/ar/products', changefreq: 'daily', priority: 0.9 },
      { loc: '/en/products', changefreq: 'daily', priority: 0.9 },
      { loc: '/ar/categories', changefreq: 'weekly', priority: 0.8 },
      { loc: '/en/categories', changefreq: 'weekly', priority: 0.8 },
      { loc: '/ar/about', changefreq: 'monthly', priority: 0.6 },
      { loc: '/en/about', changefreq: 'monthly', priority: 0.6 },
      { loc: '/ar/contact', changefreq: 'monthly', priority: 0.6 },
      { loc: '/en/contact', changefreq: 'monthly', priority: 0.6 },
    ];

    // إضافة الصفحات الثابتة
    staticPages.forEach(page => {
      paths.push({
        ...page,
        lastmod: currentTime,
      });
    });

    try {
      // جلب المنتجات من API
      console.log('🔄 Fetching products for sitemap...');
      const productsResponse = await fetch('http://localhost:3000/api/products?limit=1000', {
        headers: { 'Content-Type': 'application/json' }
      });

      if (productsResponse.ok) {
        const productsResult = await productsResponse.json();
        if (productsResult.success && productsResult.data) {
          console.log(`✅ Found ${productsResult.data.length} products for sitemap`);

          productsResult.data.forEach(product => {
            // إضافة صفحة المنتج للغتين
            paths.push({
              loc: `/ar/product/${product.id}`,
              changefreq: 'weekly',
              priority: 0.8,
              lastmod: product.updated_at || currentTime,
            });
            paths.push({
              loc: `/en/product/${product.id}`,
              changefreq: 'weekly',
              priority: 0.8,
              lastmod: product.updated_at || currentTime,
            });
          });
        }
      } else {
        console.log('⚠️ Failed to fetch products for sitemap');
      }
    } catch (error) {
      console.error('❌ Error fetching products for sitemap:', error);
    }

    try {
      // جلب الفئات من API
      console.log('🔄 Fetching categories for sitemap...');
      const categoriesResponse = await fetch('http://localhost:3000/api/categories', {
        headers: { 'Content-Type': 'application/json' }
      });

      if (categoriesResponse.ok) {
        const categoriesResult = await categoriesResponse.json();
        if (categoriesResult.success && categoriesResult.data) {
          console.log(`✅ Found ${categoriesResult.data.length} categories for sitemap`);

          categoriesResult.data.forEach(category => {
            // إضافة صفحة الفئة للغتين
            paths.push({
              loc: `/ar/category/${category.id}`,
              changefreq: 'weekly',
              priority: 0.7,
              lastmod: category.updated_at || currentTime,
            });
            paths.push({
              loc: `/en/category/${category.id}`,
              changefreq: 'weekly',
              priority: 0.7,
              lastmod: category.updated_at || currentTime,
            });
          });
        }
      } else {
        console.log('⚠️ Failed to fetch categories for sitemap');
      }
    } catch (error) {
      console.error('❌ Error fetching categories for sitemap:', error);
    }

    try {
      // جلب الفئات الفرعية من API
      console.log('🔄 Fetching subcategories for sitemap...');
      const subcategoriesResponse = await fetch('http://localhost:3000/api/subcategories', {
        headers: { 'Content-Type': 'application/json' }
      });

      if (subcategoriesResponse.ok) {
        const subcategoriesResult = await subcategoriesResponse.json();
        if (subcategoriesResult.success && subcategoriesResult.data) {
          console.log(`✅ Found ${subcategoriesResult.data.length} subcategories for sitemap`);

          subcategoriesResult.data.forEach(subcategory => {
            // إضافة صفحة الفئة الفرعية للغتين
            paths.push({
              loc: `/ar/subcategory/${subcategory.id}`,
              changefreq: 'weekly',
              priority: 0.6,
              lastmod: subcategory.updated_at || currentTime,
            });
            paths.push({
              loc: `/en/subcategory/${subcategory.id}`,
              changefreq: 'weekly',
              priority: 0.6,
              lastmod: subcategory.updated_at || currentTime,
            });
          });
        }
      } else {
        console.log('⚠️ Failed to fetch subcategories for sitemap');
      }
    } catch (error) {
      console.error('❌ Error fetching subcategories for sitemap:', error);
    }

    console.log(`🎉 Generated ${paths.length} total paths for sitemap`);
    return paths;
  },
  autoLastmod: true,
  sitemapSize: 5000,
  outDir: './public',
};
